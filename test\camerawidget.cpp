#include "camerawidget.h"
#include <QDateTime>
#include <QProcess>
#include <QFile>

const QString CameraWidget::IMAGES_DIR_NAME = "captured_images";

CameraWidget::CameraWidget(QWidget *parent)
    : QWidget(parent)
    , m_mainLayout(nullptr)
    , m_controlLayout(nullptr)
    , m_viewfinder(nullptr)
    , m_cameraComboBox(nullptr)
    , m_startStopButton(nullptr)
    , m_captureButton(nullptr)
    , m_statusLabel(nullptr)
    , m_camera(nullptr)
    , m_imageCapture(nullptr)
    , m_isCameraActive(false)
    , m_currentCameraIndex(-1)
{
    setupUI();
    setupCamera();
    connectSignals();
    
    // 创建图片保存目录
    m_imagesSaveDir = "/userdata/test/saved_images";
    QDir().mkpath(m_imagesSaveDir);
    
    qDebug() << "CameraWidget initialized";
    qDebug() << "Images save directory:" << m_imagesSaveDir;
}

CameraWidget::~CameraWidget()
{
    stopCamera();
    qDebug() << "CameraWidget destroyed";
}

void CameraWidget::setupUI()
{
    m_mainLayout = new QVBoxLayout(this);
    m_mainLayout->setContentsMargins(5, 5, 5, 5);
    m_mainLayout->setSpacing(10);
    
    // 相机预览区域
    m_viewfinder = new QCameraViewfinder(this);
    m_viewfinder->setFixedSize(VIEWFINDER_WIDTH, VIEWFINDER_HEIGHT);
    m_mainLayout->addWidget(m_viewfinder);
    
    // 控制区域
    m_controlLayout = new QHBoxLayout();
    
    // 相机选择
    m_cameraComboBox = new QComboBox(this);
    m_cameraComboBox->setMinimumWidth(150);
    m_controlLayout->addWidget(m_cameraComboBox);
    
    // 开始/停止按钮
    m_startStopButton = new QPushButton("启动相机", this);
    m_startStopButton->setMinimumHeight(35);
    m_controlLayout->addWidget(m_startStopButton);
    
    // 拍照按钮
    m_captureButton = new QPushButton("拍照", this);
    m_captureButton->setMinimumHeight(35);
    m_captureButton->setEnabled(false);
    m_controlLayout->addWidget(m_captureButton);
    
    m_controlLayout->addStretch();
    m_mainLayout->addLayout(m_controlLayout);
    
    // 状态标签
    m_statusLabel = new QLabel("相机未启动", this);
    m_statusLabel->setAlignment(Qt::AlignCenter);
    m_statusLabel->setStyleSheet("color: #888888; font-size: 11px;");
    m_mainLayout->addWidget(m_statusLabel);
    
    // 更新相机列表
    updateCameraList();
}

void CameraWidget::setupCamera()
{
    // 获取可用相机列表
    m_availableCameras = QCameraInfo::availableCameras();

    if (m_availableCameras.isEmpty()) {
        qWarning() << "No cameras available";
        m_statusLabel->setText("未检测到可用相机");
        m_startStopButton->setEnabled(false);
        return;
    }

    // 优化相机选择逻辑：优先选择 /dev/video0
    QCameraInfo defaultCamera;

    // 首先尝试找到 /dev/video0
    for (const auto &info : m_availableCameras) {
        qDebug() << "[调试] 发现相机设备:" << info.deviceName() << "描述:" << info.description();
        if (info.deviceName() == "/dev/video0") {
            defaultCamera = info;
            qDebug() << "[调试] 找到目标相机 /dev/video0";
            break;
        }
    }

    // 如果没有找到 /dev/video0，尝试其他 video 设备
    if (defaultCamera.isNull()) {
        for (const auto &info : m_availableCameras) {
            if (info.deviceName().startsWith("/dev/video") &&
                !info.deviceName().contains("camera")) {
                defaultCamera = info;
                qDebug() << "[调试] 选择备用相机:" << info.deviceName();
                break;
            }
        }
    }

    // 最后的备选方案
    if (defaultCamera.isNull() && !m_availableCameras.isEmpty()) {
        defaultCamera = m_availableCameras.first();
        qDebug() << "[调试] 使用第一个可用相机:" << defaultCamera.deviceName();
    }

    if (!defaultCamera.isNull()) {
        qDebug() << "[调试] setupCamera 最终选中 deviceName:" << defaultCamera.deviceName();
        m_camera = new QCamera(defaultCamera, this);
        m_imageCapture = new QCameraImageCapture(m_camera, this);

        // 设置相机预览
        m_camera->setViewfinder(m_viewfinder);

        // 配置图像捕获设置
        QImageEncoderSettings imageSettings;
        imageSettings.setCodec("image/jpeg");
        imageSettings.setQuality(QMultimedia::HighQuality);
        imageSettings.setResolution(1920, 1080);
        m_imageCapture->setEncodingSettings(imageSettings);

        qDebug() << "Camera setup completed with:" << defaultCamera.description();
    } else {
        qWarning() << "Failed to setup camera";
        m_statusLabel->setText("相机初始化失败");
        m_startStopButton->setEnabled(false);
    }
}

void CameraWidget::connectSignals()
{
    // 按钮信号
    connect(m_startStopButton, &QPushButton::clicked, this, &CameraWidget::onStartStopClicked);
    connect(m_captureButton, &QPushButton::clicked, this, &CameraWidget::onCaptureClicked);
    connect(m_cameraComboBox, QOverload<int>::of(&QComboBox::currentIndexChanged),
            this, &CameraWidget::onCameraSelectionChanged);
    
    // 相机信号
    if (m_camera) {
        connect(m_camera, &QCamera::stateChanged, this, &CameraWidget::onCameraStateChanged);
        connect(m_camera, QOverload<QCamera::Error>::of(&QCamera::error),
                this, &CameraWidget::onCameraError);
    }
    
    // 图像捕获信号
    if (m_imageCapture) {
        connect(m_imageCapture, &QCameraImageCapture::imageCaptured,
                this, &CameraWidget::onImageCaptured);
        connect(m_imageCapture, &QCameraImageCapture::imageSaved,
                this, &CameraWidget::onImageSaved);
        connect(m_imageCapture, QOverload<int, QCameraImageCapture::Error, const QString &>::of(&QCameraImageCapture::error),
                this, &CameraWidget::onCaptureError);
    }
}

void CameraWidget::updateCameraList()
{
    m_cameraComboBox->clear();
    
    for (int i = 0; i < m_availableCameras.size(); ++i) {
        const QCameraInfo &cameraInfo = m_availableCameras[i];
        QString displayName = QString("%1 (%2)")
                             .arg(cameraInfo.description())
                             .arg(cameraInfo.deviceName());
        m_cameraComboBox->addItem(displayName);
        
        if (cameraInfo == QCameraInfo::defaultCamera()) {
            m_cameraComboBox->setCurrentIndex(i);
            m_currentCameraIndex = i;
        }
    }
    
    if (m_availableCameras.isEmpty()) {
        m_cameraComboBox->addItem("无可用相机");
        m_cameraComboBox->setEnabled(false);
    }
}

void CameraWidget::updateUI()
{
    if (m_isCameraActive) {
        m_startStopButton->setText("停止相机");
        m_captureButton->setEnabled(true);
        m_cameraComboBox->setEnabled(false);
        m_statusLabel->setText("相机运行中");
    } else {
        m_startStopButton->setText("启动相机");
        m_captureButton->setEnabled(false);
        m_cameraComboBox->setEnabled(true);
        m_statusLabel->setText("相机已停止");
    }
}

QString CameraWidget::createImageSavePath() const
{
    QString timestamp = QDateTime::currentDateTime().toString("yyyyMMdd_hhmmss");
    QString fileName = QString("captured_%1.jpg").arg(timestamp);
    return QDir(m_imagesSaveDir).absoluteFilePath(fileName);
}

void CameraWidget::showCameraError(const QString& errorMessage)
{
    m_statusLabel->setText("错误: " + errorMessage);
    m_statusLabel->setStyleSheet("color: #ff4444; font-size: 11px;");
    emit cameraError(errorMessage);
}

void CameraWidget::startCamera()
{
    if (!m_camera) {
        showCameraError("相机未初始化");
        return;
    }
    
    if (m_isCameraActive) {
        qDebug() << "Camera already active";
        return;
    }
    
    m_camera->start();
    qDebug() << "Starting camera...";
}

void CameraWidget::stopCamera()
{
    if (!m_camera) {
        return;
    }
    
    if (!m_isCameraActive) {
        qDebug() << "Camera already stopped";
        return;
    }
    
    m_camera->stop();
    qDebug() << "Stopping camera...";
}

void CameraWidget::captureImage()
{
    qDebug() << "[调试] captureImage called, m_isCameraActive:" << m_isCameraActive
             << ", m_camera:" << (m_camera ? "not null" : "null")
             << ", camera state:" << (m_camera ? m_camera->state() : -1);

    QString imagePath = createImageSavePath();
    qDebug() << "[调试] 准备保存图片到:" << imagePath;

    // 确保保存目录存在
    QDir saveDir = QFileInfo(imagePath).absoluteDir();
    if (!saveDir.exists()) {
        saveDir.mkpath(".");
        qDebug() << "[调试] 创建保存目录:" << saveDir.absolutePath();
    }

    // 方法1：使用Qt相机API（如果相机已启动）
    if (m_imageCapture && m_isCameraActive && m_camera && m_camera->state() == QCamera::ActiveState) {
        qDebug() << "[调试] 使用Qt相机API拍照";
        m_imageCapture->capture(imagePath);
        m_statusLabel->setText("正在拍照...");
        return;
    }

    // 方法2：使用GStreamer命令（备用方案）
    qDebug() << "[调试] 使用GStreamer命令拍照";
    m_statusLabel->setText("正在拍照...");

    // 使用异步进程避免段错误
    QProcess *process = new QProcess(this);
    QString cmd = QString("timeout 10s gst-launch-1.0 v4l2src device=/dev/video0 num-buffers=1 ! jpegenc ! filesink location=%1").arg(imagePath);

    connect(process, QOverload<int, QProcess::ExitStatus>::of(&QProcess::finished),
            [this, process, imagePath](int exitCode, QProcess::ExitStatus exitStatus) {
                Q_UNUSED(exitStatus)

                if (exitCode == 0 && QFile::exists(imagePath)) {
                    qDebug() << "[调试] GStreamer拍照成功:" << imagePath;
                    emit imageCaptured(imagePath);
                    m_statusLabel->setText("图片已保存");
                } else {
                    qDebug() << "[调试] GStreamer拍照失败, exitCode:" << exitCode;
                    showCameraError("拍照失败，请重试");
                }

                process->deleteLater();
            });

    process->start("/bin/sh", QStringList() << "-c" << cmd);

    if (!process->waitForStarted(3000)) {
        qDebug() << "[调试] GStreamer进程启动失败";
        showCameraError("拍照进程启动失败");
        process->deleteLater();
    }
}

bool CameraWidget::isCameraAvailable() const
{
    return !m_availableCameras.isEmpty() && m_camera != nullptr;
}

QStringList CameraWidget::getAvailableCameras() const
{
    QStringList cameraNames;
    for (const QCameraInfo &info : m_availableCameras) {
        cameraNames.append(info.description());
    }
    return cameraNames;
}

void CameraWidget::setCurrentCamera(int index)
{
    if (index < 0 || index >= m_availableCameras.size()) {
        qWarning() << "Invalid camera index:" << index;
        return;
    }
    
    if (index == m_currentCameraIndex) {
        return;
    }
    
    // 停止当前相机
    bool wasActive = m_isCameraActive;
    if (wasActive) {
        stopCamera();
    }
    
    // 切换到新相机
    if (m_camera) {
        delete m_camera;
        m_camera = nullptr;
    }
    
    if (m_imageCapture) {
        delete m_imageCapture;
        m_imageCapture = nullptr;
    }
    
    // 创建新相机
    const QCameraInfo &cameraInfo = m_availableCameras[index];
    m_camera = new QCamera(cameraInfo, this);
    m_imageCapture = new QCameraImageCapture(m_camera, this);
    
    m_camera->setViewfinder(m_viewfinder);
    connectSignals();
    
    m_currentCameraIndex = index;
    m_cameraComboBox->setCurrentIndex(index);
    
    // 如果之前是活动状态，重新启动
    if (wasActive) {
        startCamera();
    }
    
    qDebug() << "Switched to camera:" << cameraInfo.description();
}

void CameraWidget::onCameraStateChanged(QCamera::State state)
{
    qDebug() << "[调试] onCameraStateChanged, state:" << state;
    switch (state) {
    case QCamera::ActiveState:
        m_isCameraActive = true;
        m_statusLabel->setText("相机运行中");
        m_statusLabel->setStyleSheet("color: #00aa00; font-size: 11px;");
        emit cameraStateChanged(true);
        break;
    case QCamera::LoadedState:
        m_statusLabel->setText("相机已加载");
        break;
    case QCamera::UnloadedState:
        m_isCameraActive = false;
        m_statusLabel->setText("相机已停止");
        m_statusLabel->setStyleSheet("color: #888888; font-size: 11px;");
        emit cameraStateChanged(false);
        break;
    }
    
    updateUI();
    qDebug() << "Camera state changed to:" << state;
}

void CameraWidget::onCameraError(QCamera::Error error)
{
    QString errorString;
    switch (error) {
    case QCamera::NoError:
        return;
    case QCamera::CameraError:
        errorString = "相机设备错误";
        break;
    case QCamera::InvalidRequestError:
        errorString = "无效的相机请求";
        break;
    case QCamera::ServiceMissingError:
        errorString = "相机服务不可用";
        break;
    case QCamera::NotSupportedFeatureError:
        errorString = "不支持的相机功能";
        break;
    default:
        errorString = "未知相机错误";
        break;
    }
    
    showCameraError(errorString);
    qWarning() << "Camera error:" << error << errorString;
}

void CameraWidget::onImageCaptured(int id, const QImage& image)
{
    Q_UNUSED(id)
    Q_UNUSED(image)
    
    m_statusLabel->setText("图片已捕获，正在保存...");
    qDebug() << "Image captured, ID:" << id;
}

void CameraWidget::onImageSaved(int id, const QString& fileName)
{
    Q_UNUSED(id)
    
    m_lastCapturedImagePath = fileName;
    m_statusLabel->setText("图片已保存");
    m_statusLabel->setStyleSheet("color: #00aa00; font-size: 11px;");
    
    emit imageCaptured(fileName);
    
    qDebug() << "Image saved:" << fileName;
    
    // 3秒后恢复正常状态显示
    QTimer::singleShot(3000, [this]() {
        if (m_isCameraActive) {
            m_statusLabel->setText("相机运行中");
            m_statusLabel->setStyleSheet("color: #00aa00; font-size: 11px;");
        }
    });
}

void CameraWidget::onCaptureError(int id, QCameraImageCapture::Error error, const QString& errorString)
{
    Q_UNUSED(id)
    Q_UNUSED(error)
    
    showCameraError("拍照失败: " + errorString);
    qWarning() << "Capture error:" << error << errorString;
}

void CameraWidget::onStartStopClicked()
{
    if (m_isCameraActive) {
        stopCamera();
    } else {
        startCamera();
    }
}

void CameraWidget::onCaptureClicked()
{
    qDebug() << "[调试] 拍照按钮被点击";
    captureImage();
}

void CameraWidget::onCameraSelectionChanged(int index)
{
    if (index >= 0 && index < m_availableCameras.size()) {
        setCurrentCamera(index);
    }
}
